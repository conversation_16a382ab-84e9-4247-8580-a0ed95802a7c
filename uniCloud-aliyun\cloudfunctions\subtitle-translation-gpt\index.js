// uniCloud云函数：基于GPT的高质量字幕翻译
// 参考 audio-subtitle-serverless.js 的翻译最佳实践进行优化
"use strict";

const createConfig = require("uni-config-center");

// 常量配置
const CONFIG = {
  DEFAULT_MODEL: "gpt-3.5-turbo",
  TEMPERATURE: 0.3,
  MAX_TOKENS: 4000,
  API_TIMEOUT: 60000 * 2, // 2分钟超时
  BATCH_SIZE: 100, // 批量翻译条目数量限制
};

// ASS字幕样式配置 - 统一管理所有样式相关配置
const ASS_STYLE_CONFIG = {
  // 基础样式模板
  baseStyle: {
    fontName: "Arial",
    primaryColor: "&H0000FFFF", // 亮黄色
    secondaryColor: "&H0000FFFF",
    outlineColor: "&H00000000", // 黑色边框
    backColor: "&H00000000", // 透明背景
    bold: 0,
    italic: 0,
    underline: 0,
    strikeOut: 0,
    scaleX: 100,
    scaleY: 100,
    spacing: 0,
    angle: 0,
    borderStyle: 1,
    outline: 1,
    shadow: 1,
    alignment: 2, // 底部居中
  },

  // 分辨率等级配置
  resolutionLevels: {
    "720p": { baseSize: 18, marginBase: 30 },
    "1080p": { baseSize: 40, marginBase: 40 },
    "1440p": { baseSize: 54, marginBase: 50 },
    "4k": { baseSize: 80, marginBase: 60 },
    "8k": { baseSize: 160, marginBase: 80 },
  },

  // 视频类型优化配置
  videoTypeOptimization: {
    horizontal: { fontScale: 1.0, marginScale: 1.0, sideMargin: 0 },
    vertical: { fontScale: 0.6, marginScale: 1.5, sideMargin: 20 },
    square: { fontScale: 1.0, marginScale: 1.0, sideMargin: 0 },
  },

  // 语言特定调整
  languageAdjustments: {
    zh: { marginExtra: 5, verticalExtra: 10 },
    ja: { marginExtra: 3, verticalExtra: 8 },
    ko: { marginExtra: 0, verticalExtra: 0 },
    en: { marginExtra: 0, verticalExtra: 0 },
  },

  // 脚本信息
  scriptInfo: {
    title: "Video Translation Subtitle",
    scriptType: "v4.00+",
    wrapStyle: 0,
    scaledBorderAndShadow: "yes",
    autoAdaptive: true,
  },
};

// 语言映射表 - 参考 audio-subtitle-serverless.js
const LANGUAGE_MAP = {
  zh: "中文",
  ja: "日文",
  ko: "韩文",
  fr: "法文",
  de: "德文",
  es: "西班牙文",
  it: "意大利文",
  pt: "葡萄牙文",
  ru: "俄文",
  ar: "阿拉伯文",
  hi: "印地文",
  th: "泰文",
  vi: "越南文",
  tr: "土耳其文",
  pl: "波兰文",
  nl: "荷兰文",
  sv: "瑞典文",
  da: "丹麦文",
  no: "挪威文",
  fi: "芬兰文",
};

/**
 * 使用GPT进行高质量字幕翻译
 * 参考 audio-subtitle-serverless.js 的批量翻译优化实现
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID
 * @param {string} event.sourceLanguage - 源语言代码，默认"auto"
 * @param {string} event.targetLanguage - 目标语言代码，默认"zh"
 * @returns {Object} 翻译结果
 */
exports.main = async (event, context) => {
  const startTime = Date.now();

  try {
    const { taskId, sourceLanguage = "auto", targetLanguage = "zh" } = event;

    console.log("🔄 subtitle-translation-gpt 云函数启动");
    console.log("📥 输入参数：", {
      taskId,
      sourceLanguage,
      targetLanguage,
      timestamp: new Date().toISOString(),
    });

    // 参数验证
    if (!taskId) {
      return createErrorResponse(400, "缺少必要参数：taskId");
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 验证和获取任务信息
    const { task, actualSourceLanguage } = await validateAndGetTask(
      tasksCollection,
      taskId,
      sourceLanguage
    );

    // 检查是否需要翻译
    if (actualSourceLanguage === targetLanguage) {
      console.log("🔄 源语言和目标语言相同，跳过翻译");

      await tasksCollection.doc(taskId).update({
        status: "merging",
        translationStarted: false, // 重置翻译启动标记
        updateTime: new Date(),
      });

      const processingTime = (Date.now() - startTime) / 1000;
      return createSuccessResponse("语言相同无需翻译，直接进入字幕烧录", {
        taskId,
        status: "skipped",
        reason: "same_language",
        processingTime,
      });
    }

    console.log(`🔄 开始翻译流程：${actualSourceLanguage} -> ${targetLanguage}`);

    // 获取和验证API配置
    const { apiKey, baseUrl } = await getAndValidateGptConfig();
    const model = CONFIG.DEFAULT_MODEL;

    console.log("✅ GPT配置验证通过", {
      baseUrl,
      model,
      hasApiKey: !!apiKey,
    });

    try {
      // 执行翻译流程
      console.log("📥 开始下载字幕文件...");
      const srtContent = await downloadSrtFromOSS(task.subtitleOssUrl);

      console.log("📝 解析SRT字幕文件...");
      const subtitleEntries = parseSRT(srtContent);

      if (subtitleEntries.length === 0) {
        throw new Error("字幕文件为空或格式错误");
      }

      console.log(`📊 解析完成，共 ${subtitleEntries.length} 条字幕`);

      // 执行批量翻译
      console.log("🔄 开始批量翻译...");
      const translatedEntries = await translateSubtitlesBatchOptimized(
        subtitleEntries,
        apiKey,
        baseUrl,
        model,
        actualSourceLanguage,
        targetLanguage
      );

      console.log(`✅ 翻译完成，共处理 ${translatedEntries.length} 条字幕`);

      // 生成和上传翻译后的ASS
      console.log("📤 生成并上传翻译后的字幕文件...");

      // 获取视频分辨率信息
      const videoResolution = await getVideoResolution(taskId, tasksCollection);
      const languageSpecificStyle = getLanguageSpecificStyle(
        targetLanguage,
        videoResolution.width,
        videoResolution.height
      );
      console.log(
        `🎨 使用${targetLanguage}语言优化样式: 动态字体${languageSpecificStyle.fontSize}px, 边距${languageSpecificStyle.marginV}px, 亮黄色字体+黑色边框, 分辨率${videoResolution.width}x${videoResolution.height}(${videoResolution.source})`
      );
      const translatedAssContent = generateASS(
        translatedEntries,
        targetLanguage,
        languageSpecificStyle
      );
      const uploadResult = await uploadTranslatedAssToOSS(taskId, translatedAssContent);

      // 更新任务状态为merging，准备字幕烧录，并重置翻译启动标记
      await tasksCollection.doc(taskId).update({
        status: "merging",
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        translationStarted: false, // 重置翻译启动标记
        updateTime: new Date(),
      });

      console.log("🔄 翻译完成，准备启动字幕烧录");

      // 直接启动字幕烧录，不依赖调度器
      try {
        const mergeResult = await uniCloud.callFunction({
          name: "process-video-task",
          data: {
            taskId: taskId,
            action: "merge_subtitle",
          },
        });

        console.log("✅ 字幕烧录启动结果：", mergeResult.result);

        if (mergeResult.result.code !== 200) {
          console.error("❌ 字幕烧录启动失败：", mergeResult.result.message);
          // 不抛出错误，让调度器后续处理
        }
      } catch (error) {
        console.error("❌ 字幕烧录启动异常：", error.message);
        // 不抛出错误，让调度器后续处理
      }

      const processingTime = (Date.now() - startTime) / 1000;
      console.log(`🎉 字幕翻译任务完成，耗时: ${processingTime.toFixed(2)}秒`);

      return createSuccessResponse("字幕翻译成功", {
        taskId,
        status: "completed",
        translatedCount: translatedEntries.length,
        subtitleOssUrl: uploadResult.subtitleOssUrl,
        processingTime,
        sourceLanguage: actualSourceLanguage,
        targetLanguage,
      });
    } catch (error) {
      console.error("❌ 翻译处理失败：", error);

      // 更新任务状态为失败，并重置翻译启动标记
      try {
        await tasksCollection.doc(taskId).update({
          status: "failed",
          errorMessage: "翻译失败：" + error.message,
          translationStarted: false, // 重置翻译启动标记
          updateTime: new Date(),
        });
      } catch (updateError) {
        console.error("⚠️ 更新任务状态失败：", updateError);
      }

      throw error;
    }
  } catch (error) {
    const processingTime = (Date.now() - startTime) / 1000;
    console.error("❌ subtitle-translation-gpt 云函数执行错误：", {
      error: error.message,
      stack: error.stack,
      processingTime: `${processingTime.toFixed(2)}秒`,
      timestamp: new Date().toISOString(),
    });

    return createErrorResponse(500, "字幕翻译失败: " + error.message, {
      processingTime,
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * 创建成功响应
 * @param {string} message - 成功消息
 * @param {Object} data - 响应数据
 * @returns {Object} 标准化成功响应
 */
function createSuccessResponse(message, data) {
  return {
    code: 200,
    message,
    data,
    timestamp: new Date().toISOString(),
  };
}

/**
 * 创建错误响应
 * @param {number} code - 错误代码
 * @param {string} message - 错误消息
 * @param {Object} extra - 额外信息
 * @returns {Object} 标准化错误响应
 */
function createErrorResponse(code, message, extra = {}) {
  return {
    code,
    message,
    timestamp: new Date().toISOString(),
    ...extra,
  };
}

/**
 * 验证和获取任务信息
 * @param {Object} tasksCollection - 任务集合
 * @param {string} taskId - 任务ID
 * @param {string} sourceLanguage - 源语言
 * @returns {Promise<Object>} 任务信息和实际源语言
 */
async function validateAndGetTask(tasksCollection, taskId, sourceLanguage) {
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data || taskInfo.data.length === 0) {
    throw new Error("任务不存在");
  }

  const task = taskInfo.data[0];
  if (!task.subtitleOssUrl) {
    throw new Error("缺少字幕文件地址");
  }

  // 确定实际源语言
  const actualSourceLanguage =
    sourceLanguage === "auto" ? task.recognizedLanguage || "en" : sourceLanguage;

  return { task, actualSourceLanguage };
}

/**
 * 获取和验证GPT API配置
 * @returns {Promise<Object>} API配置
 */
async function getAndValidateGptConfig() {
  const gptConfig = createConfig({
    pluginId: "openai-api",
    defaultConfig: {
      baseUrl: "https://aihubmix.com",
      model: CONFIG.DEFAULT_MODEL,
    },
  });

  const apiKey = gptConfig.config("apiKey");
  const baseUrl = gptConfig.config("baseUrl");
  const model = gptConfig.config("model");

  if (!apiKey) {
    throw new Error("GPT API配置缺失，请检查apiKey");
  }

  return { apiKey, baseUrl, model };
}

/**
 * 从OSS下载SRT文件内容
 * @param {string} ossUrl - OSS文件地址
 * @returns {Promise<string>} SRT文件内容
 */
async function downloadSrtFromOSS(ossUrl) {
  try {
    console.log("从OSS下载SRT文件：", ossUrl);

    const response = await uniCloud.httpclient.request(ossUrl, {
      method: "GET",
      timeout: 30000,
    });

    if (response.status !== 200) {
      throw new Error(`下载SRT文件失败，状态码: ${response.status}`);
    }

    // 确保返回字符串类型
    let srtContent = response.data;
    if (Buffer.isBuffer(srtContent)) {
      srtContent = srtContent.toString("utf8");
    } else if (typeof srtContent !== "string") {
      srtContent = String(srtContent);
    }

    console.log("SRT文件下载成功，内容长度：", srtContent.length);
    return srtContent;
  } catch (error) {
    console.error("下载SRT文件失败：", error);
    throw new Error(`下载SRT文件失败: ${error.message}`);
  }
}

/**
 * 解析SRT字幕格式
 * @param {string} srtContent - SRT字幕内容
 * @returns {Array} 解析后的字幕条目数组
 */
function parseSRT(srtContent) {
  // 确保输入是字符串类型
  if (typeof srtContent !== "string") {
    if (Buffer.isBuffer(srtContent)) {
      srtContent = srtContent.toString("utf8");
    } else {
      srtContent = String(srtContent || "");
    }
  }

  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n");

      if (text.trim()) {
        entries.push({
          index,
          timeRange,
          text: text.trim(),
        });
      }
    }
  }

  return entries;
}

/**
 * 优化的批量翻译字幕条目
 * 参考 audio-subtitle-serverless.js 的翻译最佳实践
 * @param {Array} entries - 字幕条目数组
 * @param {string} apiKey - API密钥
 * @param {string} baseUrl - API基础URL
 * @param {string} model - 模型名称
 * @param {string} sourceLanguage - 源语言
 * @param {string} targetLanguage - 目标语言
 * @returns {Promise<Array>} 翻译后的字幕条目数组
 */
async function translateSubtitlesBatchOptimized(
  entries,
  apiKey,
  baseUrl,
  model,
  sourceLanguage,
  targetLanguage
) {
  const translationStartTime = Date.now();
  console.log("🔄 开始批量翻译字幕", {
    totalEntries: entries.length,
    sourceLanguage,
    targetLanguage,
    model,
  });

  // 过滤和编号有效文本
  const validEntries = [];
  const originalTexts = [];

  entries.forEach((entry, index) => {
    if (entry.text?.trim()) {
      validEntries.push({ ...entry, originalIndex: index });
      originalTexts.push(`${validEntries.length}. ${entry.text.trim()}`);
    }
  });

  if (validEntries.length === 0) {
    console.log("⚠️ 没有有效的字幕文本需要翻译");
    return entries;
  }

  // 合并文本进行批量翻译
  const combinedText = originalTexts.join("\n");
  console.log(`📝 准备翻译 ${validEntries.length} 条字幕，文本长度：${combinedText.length}`);

  // 获取目标语言名称
  const targetLangName = LANGUAGE_MAP[targetLanguage] || targetLanguage.toUpperCase();

  // 构建优化的翻译请求 - 参考 audio-subtitle-serverless.js
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一位资深的多语言翻译专家。请将用户提供的带编号文本逐行译成地道、流畅且精准的 ${targetLangName}，并务必遵循以下规范：
1. 完整保留原有编号及行序，做到“原文一行，对应译文一行”。
2. 忠实传达原文含义、语气与风格，不得增删信息或加入个人解释。
3. 译文用词自然、符合 ${targetLangName} 母语者习惯，避免生硬直译。
4. 如遇专有名词、术语或品牌且无公认译法，可在译文保留原文并酌情音译或括注说明。
5. 保持所有标点符号、格式与项目符号；若原文有空行，请在译文中按相同位置保留空行。
6. 仅输出译文本身，不输出任何额外说明或标记。`,
      },
      {
        role: "user",
        content: `请将以下编号的文本翻译成${targetLangName}，保持编号格式：\n\n${combinedText}`,
      },
    ],
    temperature: CONFIG.TEMPERATURE,
    max_tokens: CONFIG.MAX_TOKENS,
  };

  console.log("📡 发送GPT翻译请求", {
    model,
    temperature: CONFIG.TEMPERATURE,
    maxTokens: CONFIG.MAX_TOKENS,
    textLength: combinedText.length,
    baseUrl,
    hasApiKey: !!apiKey,
  });

  // 检查请求参数的有效性
  if (!model || typeof model !== "string") {
    throw new Error(`无效的模型参数: ${model}`);
  }

  if (combinedText.length === 0) {
    throw new Error("翻译文本为空");
  }

  if (combinedText.length > 50000) {
    console.warn("⚠️ 翻译文本过长，可能导致API请求失败", {
      textLength: combinedText.length,
      maxRecommended: 50000,
    });
  }

  // 调用GPT API
  const apiStartTime = Date.now();
  const requestBodyStr = JSON.stringify(requestBody);

  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    data: requestBodyStr,
    dataType: "json",
    timeout: CONFIG.API_TIMEOUT,
  });

  const apiTime = (Date.now() - apiStartTime) / 1000;
  console.log(`📡 GPT API响应完成，耗时: ${apiTime.toFixed(2)}秒`);

  if (response.status !== 200) {
    // 尝试解析错误响应
    let errorMessage = `GPT API请求失败，状态码: ${response.status}`;
    let errorDetails = response.data;

    if (Buffer.isBuffer(errorDetails)) {
      try {
        errorDetails = JSON.parse(errorDetails.toString("utf8"));
      } catch (e) {
        errorDetails = errorDetails.toString("utf8");
      }
    }

    if (errorDetails && typeof errorDetails === "object" && errorDetails.error) {
      errorMessage += ` - ${errorDetails.error.message || errorDetails.error}`;
    } else if (typeof errorDetails === "string") {
      errorMessage += ` - ${errorDetails}`;
    }

    console.error("❌ GPT API请求失败", {
      status: response.status,
      errorDetails,
      requestBody: {
        model,
        messagesCount: requestBody.messages.length,
        temperature: requestBody.temperature,
        maxTokens: requestBody.max_tokens,
        textLength: combinedText.length,
      },
    });

    throw new Error(errorMessage);
  }

  const result = response.data;

  if (!result.choices?.length) {
    throw new Error("GPT API返回空结果");
  }

  const translatedText = result.choices[0].message.content.trim();
  console.log(`✅ GPT翻译完成，返回长度: ${translatedText.length}`);

  // 解析翻译结果 - 优化匹配逻辑
  const translatedLines = translatedText
    .split("\n")
    .map((line) => line.trim())
    .filter((line) => line);
  const translatedEntries = [...entries]; // 复制原数组

  console.log(`📝 解析翻译结果，共 ${translatedLines.length} 行`);

  // 为每个有效条目匹配翻译结果
  validEntries.forEach((validEntry, validIndex) => {
    const targetPattern = `${validIndex + 1}.`;
    let translatedLine = null;

    // 查找对应的翻译
    for (const line of translatedLines) {
      if (line.startsWith(targetPattern)) {
        translatedLine = line.substring(targetPattern.length).trim();
        break;
      }
    }

    if (translatedLine) {
      // 更新原始位置的条目
      translatedEntries[validEntry.originalIndex] = {
        ...validEntry,
        text: translatedLine,
      };
      console.log(`✓ 第${validIndex + 1}条翻译完成`);
    } else {
      // 翻译失败，保留原文并记录警告
      console.warn(
        `⚠️ 第${validIndex + 1}条未找到翻译，保留原文: "${validEntry.text.substring(0, 50)}..."`
      );
    }
  });

  const totalTime = (Date.now() - translationStartTime) / 1000;
  console.log(`🎯 批量翻译完成`, {
    totalEntries: entries.length,
    validEntries: validEntries.length,
    translatedLines: translatedLines.length,
    processingTime: `${totalTime.toFixed(2)}秒`,
  });

  return translatedEntries;
}

/**
 * 生成SRT字幕格式
 * @param {Array} entries - 字幕条目数组
 * @returns {string} SRT格式字符串
 */
function generateSRT(entries) {
  return entries
    .map((entry) => {
      return `${entry.index}\n${entry.timeRange}\n${entry.text}\n`;
    })
    .join("\n");
}

/**
 * 上传翻译后的SRT文件到OSS
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - 翻译后的SRT内容
 * @returns {Promise<Object>} 上传结果
 */
async function uploadTranslatedSrtToOSS(taskId, srtContent) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成翻译后SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.srt`;

    // 上传翻译后的SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传翻译后SRT文件完成，地址：", subtitleOssUrl);

    return {
      subtitleOssUrl: subtitleOssUrl,
      objectKey: objectKey,
    };
  } catch (error) {
    console.error("上传翻译后SRT文件失败：", error);
    throw new Error("上传翻译后字幕文件失败：" + error.message);
  }
}

/**
 * 上传翻译后的ASS文件到OSS（复用现有上传逻辑）
 * @param {string} taskId - 任务ID
 * @param {string} assContent - 翻译后的ASS内容
 * @returns {Promise<Object>} 上传结果
 */
async function uploadTranslatedAssToOSS(taskId, assContent) {
  const OSS = require("ali-oss");

  try {
    // 复用完全相同的OSS配置逻辑
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成翻译后ASS文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.ass`; // 仅改扩展名

    // 复用完全相同的上传逻辑
    const uploadResult = await client.put(objectKey, Buffer.from(assContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传ASS字幕文件完成，地址：", subtitleOssUrl);

    return {
      subtitleOssUrl: subtitleOssUrl,
      objectKey: objectKey,
    };
  } catch (error) {
    // 复用完全相同的错误处理逻辑
    console.error("上传ASS字幕文件失败：", error);
    throw new Error("上传ASS字幕文件失败：" + error.message);
  }
}

/**
 * 生成ASS字幕格式（完全复用现有entries结构）
 * @param {Array} entries - 复用parseSRT()返回的字幕条目数组
 * @param {string} targetLanguage - 复用现有目标语言参数
 * @param {Object} styleOverrides - 可选的样式覆盖配置
 * @returns {string} ASS格式字符串
 */
function generateASS(entries, targetLanguage, styleOverrides = {}) {
  const encoding = getLanguageEncoding(targetLanguage);

  // 合并默认配置和覆盖配置
  const style = { ...ASS_STYLE_CONFIG.baseStyle, ...styleOverrides };
  const scriptInfo = ASS_STYLE_CONFIG.scriptInfo;

  // 生成自适应分辨率的ASS头部
  const resolutionConfig = scriptInfo.autoAdaptive
    ? "" // 自适应模式：不设置固定分辨率，让播放器自动适配
    : `PlayResX: 1920\nPlayResY: 1080`;

  const assHeader = `[Script Info]
Title: ${scriptInfo.title}
ScriptType: ${scriptInfo.scriptType}
WrapStyle: ${scriptInfo.wrapStyle}
ScaledBorderAndShadow: ${scriptInfo.scaledBorderAndShadow}
${resolutionConfig}

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,${style.fontName},${style.fontSize},${style.primaryColor},${style.secondaryColor},${style.outlineColor},${style.backColor},${style.bold},${style.italic},${style.underline},${style.strikeOut},${style.scaleX},${style.scaleY},${style.spacing},${style.angle},${style.borderStyle},${style.outline},${style.shadow},${style.alignment},${style.marginL},${style.marginR},${style.marginV},${encoding}`;

  const assEvents = entries
    .map((entry, index) => {
      const { startTime, endTime } = convertTimeRange(entry.timeRange);
      const cleanText = entry.text.replace(/\r?\n/g, "\\N");
      return `Dialogue: ${index},${startTime},${endTime},Default,,0,0,0,,${cleanText}`;
    })
    .join("\n");

  return `${assHeader}\n[Events]\nFormat: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n${assEvents}`;
}

/**
 * 转换时间格式（复用现有timeRange格式）
 * @param {string} timeRange - SRT格式的时间范围
 * @returns {Object} ASS格式的开始和结束时间
 */
function convertTimeRange(timeRange) {
  const [start, end] = timeRange.split(" --> ");
  return {
    startTime: convertSRTTimeToASS(start),
    endTime: convertSRTTimeToASS(end),
  };
}

/**
 * 将SRT时间格式转换为ASS时间格式
 * @param {string} srtTime - SRT时间格式 (HH:MM:SS,mmm)
 * @returns {string} ASS时间格式 (H:MM:SS.cc)
 */
function convertSRTTimeToASS(srtTime) {
  const [time, milliseconds] = srtTime.split(",");
  const centiseconds = Math.floor(parseInt(milliseconds) / 10);
  const [hours, minutes, seconds] = time.split(":");
  return `${parseInt(hours)}:${minutes}:${seconds}.${centiseconds.toString().padStart(2, "0")}`;
}

/**
 * 获取语言编码（简化版）
 * @param {string} targetLanguage - 目标语言代码
 * @returns {number} 对应的字符编码
 */
function getLanguageEncoding(targetLanguage) {
  const encodingMap = {
    zh: 134,
    "zh-cn": 134,
    "zh-tw": 136,
    en: 0,
    ja: 128,
    ko: 129,
  };
  return encodingMap[targetLanguage] || 1;
}

/**
 * 获取视频分辨率信息（从数据库读取或使用默认值）
 * @param {string} taskId - 任务ID（用于查询视频信息）
 * @param {Object} tasksCollection - 数据库任务集合
 * @returns {Promise<Object>} 包含视频宽度和高度的对象
 */
async function getVideoResolution(taskId, tasksCollection) {
  try {
    if (taskId && tasksCollection) {
      // 从数据库查询视频分辨率信息
      const taskResult = await tasksCollection.doc(taskId).get();
      if (taskResult.data && taskResult.data.length > 0) {
        const task = taskResult.data[0];
        if (task.videoWidth && task.videoHeight) {
          console.log(`从数据库获取视频分辨率: ${task.videoWidth}x${task.videoHeight}`);
          return {
            width: task.videoWidth,
            height: task.videoHeight,
            source: "database",
          };
        }
      }
    }
  } catch (error) {
    console.warn("从数据库获取视频分辨率失败:", error.message);
  }

  // 返回常见的1080p作为默认值
  console.log("使用默认视频分辨率: 1920x1080");
  return {
    width: 1920,
    height: 1080,
    source: "default", // 标记这是默认值而非实际检测值
  };
}

/**
 * 获取分辨率等级
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {string} 分辨率等级
 */
function getResolutionLevel(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) return "1080p";

  const minDimension = Math.min(videoWidth, videoHeight);

  if (minDimension <= 720) return "720p";
  if (minDimension <= 1080) return "1080p";
  if (minDimension <= 1440) return "1440p";
  if (minDimension <= 2160) return "4k";
  return "8k";
}

/**
 * 获取视频类型
 * @param {number} videoWidth - 视频宽度
 * @param {number} videoHeight - 视频高度
 * @returns {string} 视频类型
 */
function getVideoType(videoWidth, videoHeight) {
  if (!videoWidth || !videoHeight) return "horizontal";

  if (videoHeight > videoWidth) return "vertical";
  if (videoWidth === videoHeight) return "square";
  return "horizontal";
}

/**
 * 统一的字幕样式生成器 - 整合所有样式逻辑
 * @param {string} targetLanguage - 目标语言代码
 * @param {number} videoWidth - 视频宽度（可选）
 * @param {number} videoHeight - 视频高度（可选）
 * @returns {Object} 完整的字幕样式配置
 */
function getLanguageSpecificStyle(targetLanguage, videoWidth = null, videoHeight = null) {
  // 获取基础配置
  const resolutionLevel = getResolutionLevel(videoWidth, videoHeight);
  const videoType = getVideoType(videoWidth, videoHeight);

  // 获取配置对象
  const resConfig = ASS_STYLE_CONFIG.resolutionLevels[resolutionLevel];
  const typeConfig = ASS_STYLE_CONFIG.videoTypeOptimization[videoType];
  const langConfig =
    ASS_STYLE_CONFIG.languageAdjustments[targetLanguage] ||
    ASS_STYLE_CONFIG.languageAdjustments["en"];

  console.log(`字幕样式配置: ${resolutionLevel} ${videoType}视频, 语言: ${targetLanguage}`);

  // 计算最终样式
  const fontSize = Math.round(resConfig.baseSize * typeConfig.fontScale);
  const marginV =
    Math.round(resConfig.marginBase * typeConfig.marginScale) +
    (videoType === "vertical" ? langConfig.verticalExtra : langConfig.marginExtra);

  // 返回完整样式
  return {
    ...ASS_STYLE_CONFIG.baseStyle,
    fontSize: fontSize,
    marginL: typeConfig.sideMargin,
    marginR: typeConfig.sideMargin,
    marginV: marginV,
  };
}
