<template>
  <view class="share-preview-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-animation">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载分享内容...</text>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else-if="error" class="error-container">
      <view class="error-content">
        <text class="error-icon">⚠️</text>
        <text class="error-title">{{ error.title || '加载失败' }}</text>
        <text class="error-message">{{ error.message || '无法加载分享内容' }}</text>
        <view v-if="error.needPassword" class="password-input-container">
          <input 
            v-model="password" 
            class="password-input"
            placeholder="请输入分享密码"
            password
            @confirm="handlePasswordSubmit"
          />
          <view class="password-submit" @click="handlePasswordSubmit">
            <text class="submit-text">确认</text>
          </view>
        </view>
        <view v-else class="retry-button" @click="retry">
          <text class="retry-text">重试</text>
        </view>
      </view>
    </view>

    <!-- 分享内容 -->
    <view v-else-if="shareContent" class="share-content">
      <!-- 头部信息 -->
      <view class="share-header">
        <view class="header-info">
          <text class="share-title">{{ shareContent.share.title }}</text>
          <text class="share-desc">{{ shareContent.share.description }}</text>
        </view>
        <view class="share-stats">
          <text class="stats-text">{{ shareContent.share.statistics.viewCount }} 次查看</text>
        </view>
      </view>

      <!-- 视频预览 -->
      <view class="video-container">
        <view v-if="shareContent.canPreview" class="video-wrapper">
          <video 
            v-if="shareContent.share.videoUrl"
            :src="shareContent.share.videoUrl"
            controls
            class="video-player"
            @play="handleVideoPlay"
            @ended="handleVideoEnd"
          />
          <view v-else class="video-placeholder">
            <text class="placeholder-icon">🎬</text>
            <text class="placeholder-text">视频加载中...</text>
          </view>
        </view>
        <view v-else class="preview-disabled">
          <text class="disabled-icon">🔒</text>
          <text class="disabled-text">预览已被禁用</text>
        </view>
      </view>

      <!-- 视频信息 -->
      <view class="video-info">
        <view class="info-grid">
          <view v-if="shareContent.share.metadata.duration" class="info-item">
            <text class="info-label">时长</text>
            <text class="info-value">{{ formatDuration(shareContent.share.metadata.duration) }}</text>
          </view>
          <view v-if="shareContent.share.metadata.resolution" class="info-item">
            <text class="info-label">分辨率</text>
            <text class="info-value">{{ shareContent.share.metadata.resolution }}</text>
          </view>
          <view v-if="shareContent.share.metadata.sourceLanguage" class="info-item">
            <text class="info-label">源语言</text>
            <text class="info-value">{{ shareContent.share.metadata.sourceLanguage }}</text>
          </view>
          <view v-if="shareContent.share.metadata.targetLanguage" class="info-item">
            <text class="info-label">目标语言</text>
            <text class="info-value">{{ shareContent.share.metadata.targetLanguage }}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view 
          v-if="shareContent.canDownload" 
          class="action-button download-button"
          @click="handleDownload"
        >
          <text class="button-icon">⬇️</text>
          <text class="button-text">下载视频</text>
        </view>
        <view 
          v-if="shareContent.share.subtitleUrl" 
          class="action-button subtitle-button"
          @click="handleDownloadSubtitle"
        >
          <text class="button-icon">📝</text>
          <text class="button-text">下载字幕</text>
        </view>
        <view class="action-button share-button" @click="handleShare">
          <text class="button-icon">📤</text>
          <text class="button-text">转发分享</text>
        </view>
      </view>

      <!-- 分享信息 -->
      <view class="share-footer">
        <text class="footer-text">由智能字幕胶囊制作</text>
        <text class="footer-time">{{ formatTime(shareContent.share.createdAt) }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { ShareContentResponse } from '@/types/share'
import { getShareContent, updateShareStats } from '@/utils/share'

// Props
interface Props {
  shareId: string
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'download', type: 'video' | 'subtitle'): void
  (e: 'share'): void
  (e: 'error', error: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(true)
const error = ref<any>(null)
const shareContent = ref<ShareContentResponse | null>(null)
const password = ref('')

// 加载分享内容
const loadShareContent = async (inputPassword?: string) => {
  try {
    loading.value = true
    error.value = null

    const result = await getShareContent({
      shareId: props.shareId,
      password: inputPassword
    })

    shareContent.value = result
  } catch (err: any) {
    console.error('加载分享内容失败:', err)
    error.value = {
      title: '加载失败',
      message: err.message || '无法加载分享内容',
      needPassword: err.message?.includes('密码') || err.code === 401
    }
    emit('error', err)
  } finally {
    loading.value = false
  }
}

// 处理密码提交
const handlePasswordSubmit = () => {
  if (!password.value.trim()) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    })
    return
  }
  loadShareContent(password.value.trim())
}

// 重试加载
const retry = () => {
  loadShareContent()
}

// 处理视频播放
const handleVideoPlay = () => {
  // 可以在这里添加播放统计
}

// 处理视频播放结束
const handleVideoEnd = () => {
  // 可以在这里添加播放完成统计
}

// 处理下载视频
const handleDownload = async () => {
  try {
    if (!shareContent.value?.share.videoUrl) {
      throw new Error('视频地址不存在')
    }

    // 更新下载统计
    await updateShareStats({
      shareId: props.shareId,
      action: 'download'
    })

    // 触发下载
    uni.downloadFile({
      url: shareContent.value.share.videoUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          uni.saveVideoToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })
              emit('download', 'video')
            },
            fail: () => {
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              })
            }
          })
        }
      },
      fail: () => {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
  } catch (error) {
    console.error('下载视频失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    })
  }
}

// 处理下载字幕
const handleDownloadSubtitle = async () => {
  try {
    if (!shareContent.value?.share.subtitleUrl) {
      throw new Error('字幕文件不存在')
    }

    // 更新下载统计
    await updateShareStats({
      shareId: props.shareId,
      action: 'download'
    })

    // 触发下载
    uni.downloadFile({
      url: shareContent.value.share.subtitleUrl,
      success: (res) => {
        if (res.statusCode === 200) {
          uni.showToast({
            title: '字幕下载成功',
            icon: 'success'
          })
          emit('download', 'subtitle')
        }
      },
      fail: () => {
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
  } catch (error) {
    console.error('下载字幕失败:', error)
    uni.showToast({
      title: '下载失败',
      icon: 'none'
    })
  }
}

// 处理转发分享
const handleShare = async () => {
  try {
    // 更新分享统计
    await updateShareStats({
      shareId: props.shareId,
      action: 'share'
    })

    emit('share')
  } catch (error) {
    console.error('分享失败:', error)
  }
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

// 格式化时间
const formatTime = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 组件挂载时加载内容
onMounted(() => {
  loadShareContent()
})
</script>

<style lang="scss" scoped>
.share-preview-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 64rpx;
}

.loading-animation {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.loading-spinner {
  width: 64rpx;
  height: 64rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
}

/* 错误状态样式 */
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 64rpx;
}

.error-content {
  text-align: center;
  max-width: 600rpx;
}

.error-icon {
  font-size: 96rpx;
  margin-bottom: 32rpx;
}

.error-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}

.error-message {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
  margin-bottom: 48rpx;
}

.password-input-container {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.password-input {
  flex: 1;
  padding: 24rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #ffffff;

  &:focus {
    border-color: #667eea;
  }
}

.password-submit {
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
}

.retry-button {
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 12rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.retry-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
}

/* 分享内容样式 */
.share-content {
  padding: 32rpx;
}

.share-header {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.header-info {
  margin-bottom: 24rpx;
}

.share-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

.share-desc {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.share-stats {
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.stats-text {
  font-size: 24rpx;
  color: #999999;
}

/* 视频容器样式 */
.video-container {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.video-wrapper {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
}

.video-player {
  width: 100%;
  height: 100%;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  gap: 16rpx;
}

.placeholder-icon {
  font-size: 64rpx;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 28rpx;
  color: #666666;
}

.preview-disabled {
  width: 100%;
  aspect-ratio: 16/9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  gap: 16rpx;
}

.disabled-icon {
  font-size: 64rpx;
  opacity: 0.6;
}

.disabled-text {
  font-size: 28rpx;
  color: #dc2626;
}
</style>
